import type { Metada<PERSON> } from "next";
import { Cairo } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "@/components/providers/SessionProvider";

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  variable: "--font-cairo",
});

export const metadata: Metadata = {
  title: "نظام متابعة أعمال الجمعيات التعاونية",
  description: "نظام متابعة أعمال الجمعيات التعاونية للمراقب المالي - إدارة العقود والتقارير والمهرجانات",
  keywords: ["جمعيات تعاونية", "إدارة عقود", "تقارير مالية", "مراقب مالي"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${cairo.variable} antialiased`}>
        <SessionProvider>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}
