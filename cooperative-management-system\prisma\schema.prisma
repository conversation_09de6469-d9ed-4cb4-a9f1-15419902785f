// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  password  String
  role      UserRole @default(MONITOR)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  notifications Notification[]
  archives      Archive[]

  @@map("users")
}

model Association {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  address     String
  phone       String
  managerName String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  contracts       Contract[]
  meetingMinutes  MeetingMinute[]
  discountReports DiscountReport[]
  festivals       Festival[]

  @@map("associations")
}

model Contract {
  id            String        @id @default(cuid())
  associationId String
  type          ContractType
  title         String
  startDate     DateTime
  endDate       DateTime
  amount        Float
  status        ContractStatus @default(ACTIVE)
  filePath      String?
  description   String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  association Association @relation(fields: [associationId], references: [id], onDelete: Cascade)

  @@map("contracts")
}

model MeetingMinute {
  id            String   @id @default(cuid())
  associationId String
  meetingDate   DateTime
  title         String
  content       String
  decisions     String?
  filePath      String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  association Association @relation(fields: [associationId], references: [id], onDelete: Cascade)

  @@map("meeting_minutes")
}

model DiscountReport {
  id            String     @id @default(cuid())
  associationId String
  reportType    ReportType
  period        String
  amount        Float
  status        ReportStatus @default(DRAFT)
  filePath      String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Relations
  association Association @relation(fields: [associationId], references: [id], onDelete: Cascade)

  @@map("discount_reports")
}

model Festival {
  id               String        @id @default(cuid())
  associationId    String
  name             String
  startDate        DateTime
  endDate          DateTime
  discountPercentage Float
  status           FestivalStatus @default(PLANNED)
  description      String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // Relations
  association Association @relation(fields: [associationId], references: [id], onDelete: Cascade)

  @@map("festivals")
}

model Notification {
  id         String           @id @default(cuid())
  userId     String
  title      String
  message    String
  type       NotificationType
  readStatus Boolean          @default(false)
  createdAt  DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Archive {
  id         String      @id @default(cuid())
  title      String
  fileType   FileType
  filePath   String
  category   String
  uploadedBy String
  createdAt  DateTime    @default(now())

  // Relations
  user User @relation(fields: [uploadedBy], references: [id])

  @@map("archives")
}

// Enums
enum UserRole {
  MONITOR
  MARKET_MANAGER
  GENERAL_MANAGER
}

enum ContractType {
  RENTAL
  COMMERCIAL
  STALL
}

enum ContractStatus {
  ACTIVE
  EXPIRED
  RENEWED
  CANCELLED
}

enum ReportType {
  MONTHLY
  QUARTERLY
  ANNUAL
}

enum ReportStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

enum FestivalStatus {
  PLANNED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum NotificationType {
  CONTRACT_EXPIRY
  REPORT_DUE
  FESTIVAL_REMINDER
  GENERAL
}

enum FileType {
  PDF
  WORD
  EXCEL
  IMAGE
}
