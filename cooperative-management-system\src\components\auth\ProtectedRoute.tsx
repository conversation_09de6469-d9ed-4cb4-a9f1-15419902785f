"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { hasPermission } from "@/lib/auth"

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requiredRole = "MONITOR",
  fallback 
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session) {
      router.push("/auth/signin")
      return
    }

    if (requiredRole && !hasPermission(session.user.role, requiredRole)) {
      router.push("/unauthorized")
      return
    }
  }, [session, status, router, requiredRole])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return fallback || null
  }

  if (requiredRole && !hasPermission(session.user.role, requiredRole)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            غير مصرح لك بالوصول
          </h1>
          <p className="text-gray-600">
            ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Hook for checking permissions in components
export function usePermissions() {
  const { data: session } = useSession()
  
  const checkPermission = (requiredRole: string) => {
    if (!session?.user?.role) return false
    return hasPermission(session.user.role, requiredRole)
  }

  const canView = (resource: string) => {
    if (!session?.user?.role) return false
    // Add your resource-specific permission logic here
    return true
  }

  const canEdit = (resource: string) => {
    if (!session?.user?.role) return false
    // Add your resource-specific permission logic here
    return session.user.role !== "MONITOR"
  }

  const canDelete = (resource: string) => {
    if (!session?.user?.role) return false
    // Add your resource-specific permission logic here
    return session.user.role === "GENERAL_MANAGER"
  }

  return {
    user: session?.user,
    checkPermission,
    canView,
    canEdit,
    canDelete,
    isMonitor: session?.user?.role === "MONITOR",
    isMarketManager: session?.user?.role === "MARKET_MANAGER",
    isGeneralManager: session?.user?.role === "GENERAL_MANAGER"
  }
}
