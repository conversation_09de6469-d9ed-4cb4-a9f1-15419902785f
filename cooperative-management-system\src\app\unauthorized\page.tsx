import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, ArrowRight } from "lucide-react"

export default function Unauthorized() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              غير مصرح لك بالوصول
            </CardTitle>
            <CardDescription>
              ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-sm text-gray-600">
              إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام للحصول على الصلاحيات المناسبة.
            </p>
            <div className="space-y-2">
              <Link href="/">
                <Button className="w-full">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  العودة إلى الصفحة الرئيسية
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" className="w-full">
                  تسجيل الدخول بحساب آخر
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
