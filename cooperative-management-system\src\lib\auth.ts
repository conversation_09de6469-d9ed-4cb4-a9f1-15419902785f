import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "البريد الإلكتروني", type: "email" },
        password: { label: "كلمة المرور", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
}

// Helper function to check user permissions
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'MONITOR': 1,
    'MARKET_MANAGER': 2,
    'GENERAL_MANAGER': 3
  }

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

  return userLevel >= requiredLevel
}

// Helper function to get user permissions
export function getUserPermissions(role: string) {
  const permissions = {
    MONITOR: {
      canView: ['dashboard', 'contracts', 'reports', 'meetings', 'festivals'],
      canEdit: [],
      canDelete: [],
      canCreate: []
    },
    MARKET_MANAGER: {
      canView: ['dashboard', 'contracts', 'reports', 'meetings', 'festivals'],
      canEdit: ['contracts', 'reports', 'meetings', 'festivals'],
      canDelete: ['reports'],
      canCreate: ['contracts', 'reports', 'meetings', 'festivals']
    },
    GENERAL_MANAGER: {
      canView: ['dashboard', 'contracts', 'reports', 'meetings', 'festivals', 'users', 'associations'],
      canEdit: ['contracts', 'reports', 'meetings', 'festivals', 'users', 'associations'],
      canDelete: ['contracts', 'reports', 'meetings', 'festivals', 'users'],
      canCreate: ['contracts', 'reports', 'meetings', 'festivals', 'users', 'associations']
    }
  }

  return permissions[role as keyof typeof permissions] || permissions.MONITOR
}
