"use client"

import { useState } from "react"
import { useSession, signOut } from "next-auth/react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { 
  Building2, 
  FileText, 
  Calendar, 
  TrendingUp, 
  Users, 
  Archive,
  Bell,
  Settings,
  LogOut,
  Menu,
  X
} from "lucide-react"
import { getUserRoleLabel } from "@/lib/utils"
import { usePermissions } from "@/components/auth/ProtectedRoute"

const navigation = [
  { name: "لوحة التحكم", href: "/", icon: Building2, permission: "MONITOR" },
  { name: "الجمعيات", href: "/associations", icon: Users, permission: "MARKET_MANAGER" },
  { name: "العقود", href: "/contracts", icon: FileText, permission: "MONITOR" },
  { name: "الاجتماعات", href: "/meetings", icon: Calendar, permission: "MONITOR" },
  { name: "التقارير", href: "/reports", icon: TrendingUp, permission: "MONITOR" },
  { name: "المهرجانات", href: "/festivals", icon: Calendar, permission: "MONITOR" },
  { name: "الأرشيف", href: "/archive", icon: Archive, permission: "MONITOR" },
]

export function Navbar() {
  const { data: session } = useSession()
  const { checkPermission } = usePermissions()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" })
  }

  const filteredNavigation = navigation.filter(item => 
    checkPermission(item.permission)
  )

  return (
    <nav className="bg-white shadow-sm border-b" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Desktop Navigation */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Building2 className="h-8 w-8 text-blue-600" />
              <span className="mr-2 text-xl font-bold text-gray-900">
                نظام الجمعيات التعاونية
              </span>
            </div>
            
            <div className="hidden md:mr-6 md:flex md:space-x-8 md:space-x-reverse">
              {filteredNavigation.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors"
                  >
                    <Icon className="h-4 w-4 ml-1" />
                    {item.name}
                  </Link>
                )
              })}
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Notifications */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </Button>

            {/* User Info */}
            {session?.user && (
              <div className="hidden md:flex md:items-center md:space-x-4 md:space-x-reverse">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {session.user.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {getUserRoleLabel(session.user.role)}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Button variant="ghost" size="icon">
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={handleSignOut}
                    title="تسجيل الخروج"
                  >
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="pt-2 pb-3 space-y-1 bg-gray-50">
            {filteredNavigation.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Icon className="h-5 w-5 ml-3" />
                  {item.name}
                </Link>
              )
            })}
            
            {session?.user && (
              <div className="border-t border-gray-200 pt-4 pb-3">
                <div className="px-3 space-y-1">
                  <div className="text-base font-medium text-gray-800">
                    {session.user.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {getUserRoleLabel(session.user.role)}
                  </div>
                  <Button
                    variant="ghost"
                    className="w-full justify-start mt-2"
                    onClick={handleSignOut}
                  >
                    <LogOut className="h-4 w-4 ml-2" />
                    تسجيل الخروج
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}
