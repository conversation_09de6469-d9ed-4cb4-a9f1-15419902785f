import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(amount)
}

export function isContractExpiringSoon(endDate: Date, daysThreshold: number = 30): boolean {
  const today = new Date()
  const diffTime = endDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= daysThreshold && diffDays > 0
}

export function getContractStatusColor(status: string): string {
  switch (status) {
    case 'ACTIVE':
      return 'text-green-600 bg-green-100'
    case 'EXPIRED':
      return 'text-red-600 bg-red-100'
    case 'RENEWED':
      return 'text-blue-600 bg-blue-100'
    case 'CANCELLED':
      return 'text-gray-600 bg-gray-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function getUserRoleLabel(role: string): string {
  switch (role) {
    case 'MONITOR':
      return 'مراقب'
    case 'MARKET_MANAGER':
      return 'مدير سوق'
    case 'GENERAL_MANAGER':
      return 'مدير عام'
    default:
      return role
  }
}
