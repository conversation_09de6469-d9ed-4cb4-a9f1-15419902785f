import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

export async function POST() {
  try {
    // Check if users already exist
    const existingUsers = await prisma.user.count()
    if (existingUsers > 0) {
      return NextResponse.json({ message: "المستخدمون موجودون بالفعل" }, { status: 400 })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash("password123", 12)

    // Create demo users
    const users = await Promise.all([
      prisma.user.create({
        data: {
          name: "مراقب مالي",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "MON<PERSON><PERSON>"
        }
      }),
      prisma.user.create({
        data: {
          name: "مدير السوق",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "MARKET_MANAGER"
        }
      }),
      prisma.user.create({
        data: {
          name: "المدير العام",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "GENERAL_MANAGER"
        }
      })
    ])

    // Create demo associations
    const associations = await Promise.all([
      prisma.association.create({
        data: {
          name: "جمعية الرياض التعاونية",
          code: "RYD001",
          address: "الرياض، حي النخيل",
          phone: "0112345678",
          managerName: "أحمد محمد السعد"
        }
      }),
      prisma.association.create({
        data: {
          name: "جمعية جدة التعاونية",
          code: "JED002",
          address: "جدة، حي الصفا",
          phone: "0126789012",
          managerName: "فاطمة علي الأحمد"
        }
      }),
      prisma.association.create({
        data: {
          name: "جمعية الدمام التعاونية",
          code: "DMM003",
          address: "الدمام، حي الفيصلية",
          phone: "0133456789",
          managerName: "محمد عبدالله الخالد"
        }
      })
    ])

    // Create demo contracts
    const contracts = await Promise.all([
      prisma.contract.create({
        data: {
          associationId: associations[0].id,
          type: "RENTAL",
          title: "عقد إيجار محل رقم 1",
          startDate: new Date("2024-01-01"),
          endDate: new Date("2024-12-31"),
          amount: 50000,
          status: "ACTIVE",
          description: "عقد إيجار محل تجاري في الدور الأرضي"
        }
      }),
      prisma.contract.create({
        data: {
          associationId: associations[1].id,
          type: "COMMERCIAL",
          title: "عقد تجاري - مطعم الأصالة",
          startDate: new Date("2024-06-01"),
          endDate: new Date("2025-05-31"),
          amount: 75000,
          status: "ACTIVE",
          description: "عقد تجاري لتشغيل مطعم"
        }
      })
    ])

    return NextResponse.json({ 
      message: "تم إنشاء البيانات التجريبية بنجاح",
      users: users.length,
      associations: associations.length,
      contracts: contracts.length
    })

  } catch (error) {
    console.error("Error seeding database:", error)
    return NextResponse.json(
      { error: "حدث خطأ أثناء إنشاء البيانات التجريبية" },
      { status: 500 }
    )
  }
}
