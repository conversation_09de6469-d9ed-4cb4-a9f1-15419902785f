import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Building2,
  FileText,
  TrendingUp,
  Calendar,
  Bell,
  Users,
  DollarSign,
  AlertTriangle
} from "lucide-react"

export default function Dashboard() {
  // Mock data - في التطبيق الحقيقي سيتم جلب هذه البيانات من قاعدة البيانات
  const stats = {
    totalAssociations: 12,
    activeContracts: 45,
    expiringContracts: 8,
    monthlyRevenue: 125000,
    pendingReports: 3,
    upcomingMeetings: 2
  }

  const recentActivities = [
    { id: 1, type: 'contract', message: 'عقد إيجار جديد - جمعية الرياض التعاونية', time: '2 ساعات' },
    { id: 2, type: 'report', message: 'تقرير خصومات شهر ديسمبر - جمعية جدة', time: '4 ساعات' },
    { id: 3, type: 'meeting', message: 'اجتماع مجلس الإدارة - جمعية الدمام', time: '1 يوم' },
    { id: 4, type: 'festival', message: 'مهرجان التخفيضات الشتوي بدأ', time: '2 أيام' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6" dir="rtl">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            لوحة التحكم الرئيسية
          </h1>
          <p className="text-gray-600">
            نظام متابعة أعمال الجمعيات التعاونية للمراقب المالي
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الجمعيات</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalAssociations}</div>
              <p className="text-xs text-muted-foreground">
                +2 من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العقود النشطة</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeContracts}</div>
              <p className="text-xs text-muted-foreground">
                +5 من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">عقود تنتهي قريباً</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.expiringContracts}</div>
              <p className="text-xs text-muted-foreground">
                خلال 30 يوم
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.monthlyRevenue.toLocaleString()} ريال</div>
              <p className="text-xs text-muted-foreground">
                +12% من الشهر الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activities */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  الأنشطة الأخيرة
                </CardTitle>
                <CardDescription>
                  آخر التحديثات والأنشطة في النظام
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                      <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {activity.message}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          منذ {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button variant="outline" className="w-full">
                    عرض جميع الأنشطة
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>الإجراءات السريعة</CardTitle>
                <CardDescription>
                  الوصول السريع للمهام الأساسية
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 ml-2" />
                  إضافة عقد جديد
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Calendar className="h-4 w-4 ml-2" />
                  جدولة اجتماع
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <TrendingUp className="h-4 w-4 ml-2" />
                  إنشاء تقرير
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة الجمعيات
                </Button>
              </CardContent>
            </Card>

            {/* Alerts */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-orange-600">تنبيهات مهمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2 p-2 bg-orange-50 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-orange-800">
                        {stats.expiringContracts} عقود تنتهي خلال 30 يوم
                      </p>
                      <p className="text-xs text-orange-600">
                        تحتاج لمراجعة وتجديد
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 p-2 bg-blue-50 rounded-lg">
                    <FileText className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {stats.pendingReports} تقارير في انتظار المراجعة
                      </p>
                      <p className="text-xs text-blue-600">
                        تحتاج للموافقة
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
